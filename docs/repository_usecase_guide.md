# Documentation Technique: Repositories et Usecases

## Introduction

Cette documentation explique l'organisation et le fonctionnement du code lié aux **repositories** et **usecases** dans le projet. L'objectif est de clarifier les responsabilités de chaque couche et d'assurer une meilleure maintenabilité pour les futurs développeurs.

## Structure Générale

- **Repositories** : contiennent les appels à la base de données via Supabase.
- **Usecases** : contiennent la logique métier (préparation des données, appels multiples, validation, etc.).

---

## 1. Repositories

### 📁 Emplacement

```bash
./src/infrastructure/repository/<nom-table>/
```

### 📌 Exemple de structure

```bash
./src/infrastructure/repositories/stocks
├── constants.ts
├── CreateProfessionalStocksRepository.ts
├── DeleteProfessionalStockRepository.ts
├── GetProfessionalStockByStockIdRepository.ts
├── GetProfessionalStocksRepository.ts
├── index.ts
└── UpdateProfessionalStockRepository.ts
```

### 📖 Explications

- `constants.ts` : Contient le nom de la table pour centraliser l'information et éviter les fautes de frappe.

```ts
export const PROFESSIONAL_STOCKS_TABLE_NAME = "stocks";
```

- `index.ts` : Exporte toutes les classes de repository disponibles pour cette table.

```ts
export * from "./CreateProfessionalStocksRepository";
export * from "./GetProfessionalStockByStockIdRepository";
export * from "./GetProfessionalStocksRepository";
export * from "./UpdateProfessionalStockRepository";
export * from "./DeleteProfessionalStockRepository";
```

- Les autres fichiers (`Create`, `Update`, `Delete`, etc.) : représentent les actions réalisables sur la table. Chaque fichier contient une classe qui implémente une interface bien définie.

#### Exemple : Création d'un stock professionnel

```ts
import { Stocks } from "@/domain/models/Stocks.ts";
import { supabase } from "@/infrastructure/supabase/supabase.ts";
import { PROFESSIONAL_STOCKS_TABLE_NAME } from "./constants.ts";
import { ICreateProfessionalStocksRepository } from "@/domain/interfaces/repositories/stocks/ICreateProfessionalStocksRepository.ts";

class CreateProfessionalStocksRepository implements ICreateProfessionalStocksRepository {
  async execute(stocks: Stocks[]) {
    const { data, error } = await supabase
      .from(PROFESSIONAL_STOCKS_TABLE_NAME)
      .insert(stocks)
      .select();

    if (error) throw error;
    return data || [];
  }
}

export default CreateProfessionalStocksRepository;
```

---

## 2. Interfaces des Repositories

### 📁 Emplacement

```bash
./src/domain/interfaces/repositories/<nom-table>/
```

### 📌 Exemple de structure

```bash
./src/domain/interfaces/repositories/stocks
├── ICreateProfessionalStocksRepository.ts
├── IDeleteProfessionalStockRepository.ts
├── IGetProfessionalStocksRepository.ts
├── IGetProfessionalStockByStockIdRepository.ts
├── index.ts
└── IUpdateProfessionalStockRepository.ts
```

### 📖 Explications

- Chaque fichier contient une interface pour une opération spécifique.
- `index.ts` exporte toutes les interfaces pour simplifier les imports ailleurs.

#### Exemple d'interface

```ts
// ICreateProfessionalStocksRepository.ts
import { Stocks } from "@/domain/models/Stocks.ts";

export interface ICreateProfessionalStocksRepository {
  execute(stocks: Omit<Stocks, "id">[]): Promise<Stocks[]>;
}
```

---

## 3. Usecases

### 📁 Emplacement

```bash
./src/domain/usecases/<nom-table>/
```

### 📌 Exemple de structure

```bash
./src/domain/usecases/stocks
├── CreateProfessionalStocksUsecase.ts
├── DeleteProfessionalStockUsecase.ts
├── GetProfessionalStocksByStockIdUsecase.ts
├── GetProfessionalStocksUsecase.ts
├── index.ts
└── UpdateProfessionalStocksUsecase.ts
```

### 📖 Explications

- Les usecases utilisent les interfaces des repositories comme dépendances.
- Ils contiennent la logique métier (validation, formatage, combinaison de données, etc.).

#### Exemple : Récupérer un stock par son ID

```ts
import { IGetProfessionalStockByStockIdRepository } from "@/domain/interfaces/repositories/stocks/IGetProfessionalStockByStockIdRepository";
import { IGetProfessionalByStockIdUsecase } from "@/domain/interfaces/usecases/stocks/IGetProfessionalByStockIdUsecase";

class GetProfessionalStockByStockIdUsecase implements IGetProfessionalByStockIdUsecase {
  constructor(
    private readonly getProfessionalStockByStockIdRepository: IGetProfessionalStockByStockIdRepository
  ) {}

  async execute(stockId: number) {
    return this.getProfessionalStockByStockIdRepository.execute(stockId);
  }
}

export default GetProfessionalStockByStockIdUsecase;
```

---

## 4. Exemple Avancé de Usecase (Multi-dépendances)

Certains cas complexes comme la connexion d'utilisateur utilisent plusieurs dépendances.

```ts
export class LoginUserUsecase implements ILoginUserUsecase {
  constructor(
    private readonly getUserByEmailUsecase: IGetUserByEmailUsecase,
    private readonly authenticateUserUsecase: IAuthenticateUserUsecase,
    private readonly passwordService: IPasswordService,
    private readonly userInformationProviderFactory: UserInformationProviderFactory,
  ) {}

  async getUserInformations(role: utilisateurs_role_enum, id: number) {
    const provider = this.userInformationProviderFactory.createProvider(role);
    return provider.getUserInformation(id);
  }

  async isValidPassword(correctPassword: string, credentialPassword: string): Promise<LoginUserDTO> {
    const isPasswordValid = await this.passwordService.comparePasswords(
      credentialPassword,
      correctPassword,
    );

    if (!isPasswordValid) {
      return {
        userInformations: null,
        user: null,
        success: false,
        error: new Error(ErrorMessages.CREDENTIAL_INVALID),
      };
    }
  }

  async execute(credential: loginCredential): Promise<LoginUserDTO> {
    if (!navigator.onLine) throw new Error("Aucune connexion internet");

    const matchingUser = await this.getUserByEmailUsecase.execute(credential.email);

    if (!matchingUser) throw new Error(ErrorMessages.CREDENTIAL_INVALID);

    await this.authenticateUserUsecase.execute(credential);

    return {
      userInformations: await this.getUserInformations(matchingUser.role, matchingUser.id),
      user: matchingUser,
      success: true,
      error: null,
    };
  }
}
```

---

## 5. Typage des Données

Toutes les données utilisées dans les repositories ou usecases sont typées à l'aide des modèles présents dans :

```bash
./src/domain/models/
```

Ces modèles représentent les schémas des tables de la base de données.

---

## ✅ Résumé

| Élément      | Contenu                                             |
| ------------ | --------------------------------------------------- |
| Repositories | Interaction avec la base via Supabase               |
| Interfaces   | Définition contractuelle des méthodes de repository |
| Usecases     | Logique métier, manipulation des données            |
| Models       | Représentation typée des entités de la base         |

---

## 🔚 Conclusion

Cette architecture modulaire permet une séparation claire des responsabilités :

- Facilité de test
- Meilleure lisibilité
- Réutilisabilité accrue

Elle est fortement inspirée des principes de Clean Architecture. En suivant ces conventions, vous contribuerez à maintenir un code propre, robuste et facilement évolutif.

