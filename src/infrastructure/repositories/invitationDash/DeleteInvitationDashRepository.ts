import { supabase } from "@/infrastructure/supabase/supabase";
import { INVITATION_DASH_TABLE_NAME } from "./constants";
import { IDeleteInvitationDashRepository } from "@/domain/interfaces/repositories/invitationDash/IDeleteInvitationDashRepository";

class DeleteInvitationDashRepository implements IDeleteInvitationDashRepository {
  constructor() {}

  async execute(invitation_id: number): Promise<void> {
    const { error } = await supabase
      .from(INVITATION_DASH_TABLE_NAME)
      .delete()
      .eq("id", invitation_id);

    if (error) {
      throw error;
    }
  }
}

export default DeleteInvitationDashRepository;
