import {
  getDashInvitations as getDashInvitationsAction,
  deleteDashInvitation as deleteDashInvitationAction,
  markDashInvitationAsUsed as markDashInvitationAsUsedAction,
  generateDashInvitation as generateDashInvitationAction,
} from "@/application/slices/admin/dashInvitationsSlice.ts";
import { AppDispatch, RootState } from "@/store/index";
import { useDispatch, useSelector } from "react-redux";

const useDashInvitations = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { invitations, loading, error } = useSelector(
    (state: RootState) => state.dashInvitations
  );

  const getDashInvitations = () => {
    dispatch(getDashInvitationsAction());
  };
  const deleteDashInvitation = (id: number) => {
    dispatch(deleteDashInvitationAction(id));
  };
  const markDashInvitationAsUsed = (id: number) => {
    dispatch(markDashInvitationAsUsedAction(id));
  };

  const generateDashInvitation = (email: string, organizationName: string) => {
    return dispatch(generateDashInvitationAction({ email, organizationName }));
  };

  return {
    getDashInvitations,
    invitations,
    loading,
    error,
    deleteDashInvitation,
    markDashInvitationAsUsed,
    generateDashInvitation,
  };
};

export default useDashInvitations;
