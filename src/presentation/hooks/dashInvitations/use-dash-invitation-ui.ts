import { useState, useMemo } from "react";
import { InvitationDash } from "@/domain/models";
import useDashInvitations from "./use-dash-invitations";

/**
 * Hook personnalisé pour la gestion de l'interface utilisateur des invitations dash
 * Gère le filtrage, la recherche et les actions sur les invitations
 */
export const useDashInvitationUI = () => {
  // États locaux pour l'interface utilisateur
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [generateModalOpen, setGenerateModalOpen] = useState(false);
  const [selectedInvitation, setSelectedInvitation] =
    useState<InvitationDash | null>(null);

  // Hook pour les données des invitations
  const {
    invitations,
    loading,
    error,
    getDashInvitations,
    deleteDashInvitation,
    markDashInvitationAsUsed,
    generateDashInvitation,
  } = useDashInvitations();

  /**
   * Filtrage des invitations basé sur la recherche et le statut
   */
  const filteredInvitations = useMemo(() => {
    if (!invitations) return [];

    return invitations.filter((invitation) => {
      // Filtrage par recherche (email)
      const matchesSearch = invitation.email
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

      // Filtrage par statut
      const matchesStatus =
        statusFilter === "all" ||
        (statusFilter === "used" && invitation.est_utilisee) ||
        (statusFilter === "unused" && !invitation.est_utilisee);

      return matchesSearch && matchesStatus;
    });
  }, [invitations, searchQuery, statusFilter]);

  /**
   * Gestionnaire pour charger les invitations
   */
  const handleGetDashInvitations = () => {
    getDashInvitations();
  };

  /**
   * Gestionnaire pour voir les détails d'une invitation
   */
  const handleViewDetails = (invitation: InvitationDash) => {
    setSelectedInvitation(invitation);
    setDetailModalOpen(true);
  };

  /**
   * Gestionnaire pour marquer une invitation comme utilisée
   */
  const handleMarkAsUsed = async (id: number) => {
    try {
      await markDashInvitationAsUsed(id);
      // Fermer le modal après l'action
      setDetailModalOpen(false);
      setSelectedInvitation(null);
    } catch (error) {
      console.error("Erreur lors du marquage comme utilisée:", error);
    }
  };

  /**
   * Gestionnaire pour supprimer une invitation
   */
  const handleDelete = async (id: number) => {
    try {
      await deleteDashInvitation(id);
      // Fermer le modal après l'action
      setDetailModalOpen(false);
      setSelectedInvitation(null);
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
    }
  };

  /**
   * Gestionnaire pour générer une nouvelle invitation
   */
  const handleGenerateInvitation = async (
    email: string,
    organizationName: string
  ) => {
    try {
      await generateDashInvitation(email, organizationName).unwrap();
      // Fermer le modal après génération réussie
      setGenerateModalOpen(false);
      // Rafraîchir la liste des invitations
      getDashInvitations();
    } catch (error) {
      console.error("Erreur lors de la génération de l'invitation:", error);
      throw error; // Relancer l'erreur pour que le composant puisse la gérer
    }
  };

  /**
   * Gestionnaire pour ouvrir le modal de génération
   */
  const handleOpenGenerateModal = () => {
    setGenerateModalOpen(true);
  };

  /**
   * Gestionnaire pour fermer le modal de génération
   */
  const closeGenerateModal = () => {
    setGenerateModalOpen(false);
  };

  /**
   * Gestionnaire pour fermer le modal de détails
   */
  const closeDetailModal = () => {
    setDetailModalOpen(false);
    setSelectedInvitation(null);
  };

  return {
    // États de l'interface
    searchQuery,
    statusFilter,
    detailModalOpen,
    generateModalOpen,
    selectedInvitation,

    // Données filtrées
    filteredInvitations,

    // États de chargement
    loading,
    error,

    // Setters pour les filtres
    setSearchQuery,
    setStatusFilter,

    // Actions
    handleGetDashInvitations,
    handleViewDetails,
    handleMarkAsUsed,
    handleDelete,
    handleGenerateInvitation,
    handleOpenGenerateModal,
    closeDetailModal,
    closeGenerateModal,
  };
};
