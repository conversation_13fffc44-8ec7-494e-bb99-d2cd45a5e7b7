import { useEffect } from "react";
import { Box, CircularProgress, Alert } from "@mui/material";
import { DashInvitationHeader } from "@/presentation/components/common/dashInvitations/DashInvitationHeader";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { DashInvitationDetailModal } from "@/presentation/components/features/admin/dashInvitations/DashInvitationDetailModal";
import { GenerateDashInvitationModal } from "@/presentation/components/features/admin/dashInvitations/GenerateDashInvitationModal";
import { useDashInvitationUI } from "@/presentation/hooks/dashInvitations/use-dash-invitation-ui.ts";
import { DashInvitationProvider } from "@/presentation/components/features/admin/dashInvitations/DashInvitationContext";

/**
 * Page d'administration des invitations dash
 * Utilise le composant ListDataGrid existant avec des hooks personnalisés
 * pour la gestion des données et des colonnes
 */
const Dash = () => {
  // Utilisation du hook personnalisé pour la gestion de l'UI
  const {
    searchQuery,
    statusFilter,
    detailModalOpen,
    generateModalOpen,
    selectedInvitation,
    filteredInvitations,
    loading,
    error,
    setSearchQuery,
    setStatusFilter,
    handleGetDashInvitations,
    handleViewDetails,
    handleMarkAsUsed,
    handleDelete,
    handleGenerateInvitation,
    handleOpenGenerateModal,
    closeDetailModal,
    closeGenerateModal,
  } = useDashInvitationUI();

  // Chargement initial des données
  useEffect(() => {
    handleGetDashInvitations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const contextValue = {
    handleViewDetails,
    handleMarkAsUsed,
    handleDelete,
  };

  return (
    <DashInvitationProvider value={contextValue}>
      <Box className="p-6 mx-auto max-w-full overflow-hidden">
        <DashInvitationHeader
          searchQuery={searchQuery}
          statusFilter={statusFilter}
          onSearchChange={setSearchQuery}
          onStatusFilterChange={setStatusFilter}
          onGenerateClick={handleOpenGenerateModal}
        />

        {error && (
          <Alert severity="error" className="mb-4">
            {error}
          </Alert>
        )}

        {loading && filteredInvitations.length === 0 ? (
          <Box className="flex justify-center items-center h-64">
            <CircularProgress />
          </Box>
        ) : (
          <Box className="overflow-x-auto">
            <ListDataGrid
              data={filteredInvitations}
              type="dash_invitations"
              className="overflow-y-auto w-full"
            />
          </Box>
        )}

        <DashInvitationDetailModal
          open={detailModalOpen}
          onClose={closeDetailModal}
          data={selectedInvitation}
          onMarkAsUsed={handleMarkAsUsed}
          onDelete={handleDelete}
        />

        <GenerateDashInvitationModal
          open={generateModalOpen}
          onClose={closeGenerateModal}
          onGenerate={handleGenerateInvitation}
        />
      </Box>
    </DashInvitationProvider>
  );
};

const DashPage = Dash;
export default DashPage;
