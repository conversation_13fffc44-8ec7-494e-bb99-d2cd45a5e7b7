import React from "react";
import { DataGrid } from "@mui/x-data-grid";
import { Paper } from "@mui/material";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { localeText } from "@/shared/constants/localeText";
import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import { active_tab_enum, utilisateurs_role_enum } from "@/domain/models/enums";
import { AppointmentColumnsProfessional } from "@/presentation/components/features/professional/appointment/AppointmentColumnsProfessional";
import { AppointmentColumnsPatient } from "@/presentation/components/features/patient/appointments/AppointmentColumnsPatient";
import { FacturationDTO } from "@/domain/DTOS";
import { FacturationColumns } from "../historiqueCarnetSante/component/FacturationColumns";
import { FacturationProfessionalColumns } from "../../features/facturation/FacturationProfessionalColumns";
import {
  demande_adhesion,
  Proche,
  Stocks,
  InvitationDash,
} from "@/domain/models";
import { AdhesionRequestColumns } from "../../features/admin/adhesionRequests/AdhesionRequestColumns";
import { twMerge } from "tailwind-merge";
import { ProcheColumns } from "@/presentation/pages/patient/families/ProcheColumns";
import { StockColumns } from "../../features/professional/stock/StockColumns.tsx";
import { DashInvitationColumns } from "../../features/admin/dashInvitations/DashInvitationColumns";

interface ListDataGridProps extends React.ComponentProps<"div"> {
  data:
    | AppointmentProfessionalDTO[]
    | AppointmentPatientDTO[]
    | FacturationDTO[]
    | demande_adhesion[]
    | Proche[]
    | Stocks[]
    | InvitationDash[];
  type: string;
}

const getColumns = (type: string) => {
  switch (type) {
    case utilisateurs_role_enum.PROFESSIONNEL:
      return AppointmentColumnsProfessional();
    case utilisateurs_role_enum.PATIENT:
      return AppointmentColumnsPatient();
    case active_tab_enum.facturation:
      return FacturationColumns();
    case "facturation_professional":
      return FacturationProfessionalColumns();
    case "adhesion_requests":
      return AdhesionRequestColumns();
    case "proche":
      return ProcheColumns();
    case "stock":
      return StockColumns();
    case "dash_invitations":
      return DashInvitationColumns();
    default:
      return [];
  }
};

const ListDataGrid = ({ data, type, className }: ListDataGridProps) => {
  const columns = getColumns(type);

  return (
    <Paper
      component="div"
      className={twMerge("h-[371px] w-full shadow-lg", className)}
    >
      <DataGrid
        rows={data}
        columns={columns}
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: 5 },
          },
        }}
        pageSizeOptions={[5, 10, 20]}
        localeText={localeText}
        sx={{
          "& .font-semibold": {
            fontWeight: "bold",
          },
        }}
        disableRowSelectionOnClick
        disableVirtualization
      />
    </Paper>
  );
};

export default ListDataGrid;
