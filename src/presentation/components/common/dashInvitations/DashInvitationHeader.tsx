import React from "react";
import {
  <PERSON>po<PERSON>,
  Box,
  TextField,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
  Button,
} from "@mui/material";
import { Search, Plus } from "lucide-react";
import {
  GREEN,
  EVENT_COLOR,
  DESTRUCTIVE,
  PRIMARY,
} from "@/shared/constants/Color";

interface DashInvitationHeaderProps {
  title?: string;
  description?: string;
  searchQuery?: string;
  statusFilter?: string;
  onSearchChange?: (query: string) => void;
  onStatusFilterChange?: (status: string) => void;
  onGenerateClick?: () => void;
}

/**
 * Header pour la page des invitations dash
 * Contient le titre, la description et les contrôles de filtrage
 */
export const DashInvitationHeader: React.FC<DashInvitationHeaderProps> = ({
  title = "Invitations Dash",
  description = "Gérez les invitations pour accéder aux tableaux de bord",
  searchQuery = "",
  statusFilter = "all",
  onSearch<PERSON>hange,
  onStatusFilter<PERSON>hange,
  onGenerateClick,
}) => {
  return (
    <Box className="mb-6">
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-start",
          mb: { xs: 2, sm: 3 },
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              mb: 1,
              fontWeight: 600,
              fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2rem" },
              color: "#07294A",
              letterSpacing: "-0.01em",
            }}
          >
            {title}
          </Typography>

          <Typography
            variant="body1"
            sx={{
              fontSize: { xs: "0.875rem", sm: "1rem" },
              lineHeight: 1.6,
              color: "#07294A",
              fontWeight: 400,
            }}
          >
            {description}
          </Typography>
        </Box>

        {onGenerateClick && (
          <Button
            variant="contained"
            startIcon={<Plus size={20} />}
            onClick={onGenerateClick}
            sx={{
              backgroundColor: PRIMARY,
              "&:hover": { backgroundColor: "#1976d2" },
              borderRadius: "8px",
              textTransform: "none",
              fontWeight: 500,
              px: 3,
              py: 1.5,
              minWidth: "auto",
              whiteSpace: "nowrap",
            }}
          >
            Générer une invitation
          </Button>
        )}
      </Box>

      {(onSearchChange || onStatusFilterChange) && (
        <Box
          sx={{
            mt: 3,
            display: "flex",
            gap: 2,
            flexWrap: { xs: "wrap", sm: "nowrap" },
            width: "100%",
          }}
        >
          {onSearchChange && (
            <TextField
              fullWidth
              placeholder="Rechercher par email..."
              variant="outlined"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search size={20} />
                  </InputAdornment>
                ),
              }}
              sx={{
                flex: "1 1 auto",
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                },
                "& .MuiInputBase-root": {
                  paddingLeft: "12px",
                },
              }}
            />
          )}

          {onStatusFilterChange && (
            <FormControl
              sx={{ width: { xs: "100%", sm: "250px" }, flex: "0 0 auto" }}
            >
              <Select
                value={statusFilter}
                onChange={(e: SelectChangeEvent<string>) =>
                  onStatusFilterChange(e.target.value)
                }
                displayEmpty
                fullWidth
                variant="outlined"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                  },
                  height: "56px",
                }}
              >
                <MenuItem value="all">
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: "#CCCCCC",
                      }}
                    />
                    Tous les statuts
                  </Box>
                </MenuItem>
                <MenuItem value="unused">
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: EVENT_COLOR,
                      }}
                    />
                    Non utilisées
                  </Box>
                </MenuItem>
                <MenuItem value="used">
                  <Box
                    sx={{ display: "flex", alignItems: "center", gap: "8px" }}
                  >
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        bgcolor: GREEN,
                      }}
                    />
                    Utilisées
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
          )}
        </Box>
      )}
    </Box>
  );
};

export default DashInvitationHeader;
