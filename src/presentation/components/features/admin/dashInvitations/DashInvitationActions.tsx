import React, { useState, useContext } from "react";
import {
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from "@mui/material";
import {
  MoreV<PERSON>ical,
  Eye,
  CheckCircle,
  Trash2,
  Al<PERSON><PERSON>riangle,
} from "lucide-react";
import { InvitationDash } from "@/domain/models";
import { DashInvitationContext } from "./DashInvitationContext";
import { DESTRUCTIVE } from "@/shared/constants/Color";

interface DashInvitationActionsProps {
  row: InvitationDash;
}

/**
 * Composant d'actions pour les invitations dash dans le DataGrid
 * Affiche un menu avec les actions disponibles
 */
export const DashInvitationActions: React.FC<DashInvitationActionsProps> = ({
  row,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const open = Boolean(anchorEl);

  const context = useContext(DashInvitationContext);
  if (!context) {
    throw new Error(
      "DashInvitationActions must be used within DashInvitationContext"
    );
  }

  const { handleViewDetails, handleMarkAsUsed, handleDelete } = context;

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = (event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    setAnchorEl(null);
  };

  const handleViewDetailsClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    handleViewDetails(row);
    handleClose();
  };

  const handleMarkAsUsedClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    handleMarkAsUsed(row.id);
    handleClose();
  };

  const handleDeleteClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    setDeleteDialogOpen(true);
    handleClose();
  };

  const handleConfirmDelete = () => {
    handleDelete(row.id);
    setDeleteDialogOpen(false);
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
  };

  return (
    <>
      <IconButton
        size="small"
        onClick={handleClick}
        sx={{
          "&:hover": {
            backgroundColor: "rgba(0, 0, 0, 0.04)",
          },
        }}
      >
        <MoreVertical size={16} />
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={() => handleClose()}
        onClick={(e) => e.stopPropagation()}
        PaperProps={{
          sx: {
            borderRadius: "8px",
            boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
            minWidth: 180,
          },
        }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <MenuItem onClick={handleViewDetailsClick}>
          <ListItemIcon>
            <Eye size={16} />
          </ListItemIcon>
          <ListItemText primary="Voir détails" />
        </MenuItem>

        {!row.est_utilisee && (
          <MenuItem onClick={handleMarkAsUsedClick}>
            <ListItemIcon>
              <CheckCircle size={16} />
            </ListItemIcon>
            <ListItemText primary="Marquer comme utilisée" />
          </MenuItem>
        )}

        <MenuItem
          onClick={handleDeleteClick}
          sx={{
            color: "error.main",
            "&:hover": {
              backgroundColor: "error.light",
              color: "error.contrastText",
            },
          }}
        >
          <ListItemIcon sx={{ color: "inherit" }}>
            <Trash2 size={16} />
          </ListItemIcon>
          <ListItemText primary="Supprimer" />
        </MenuItem>
      </Menu>

      {/* Dialog de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 2,
            color: DESTRUCTIVE,
          }}
        >
          <AlertTriangle size={24} />
          Confirmer la suppression
        </DialogTitle>

        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Êtes-vous sûr de vouloir supprimer cette invitation ?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            <strong>Email :</strong> {row.email}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>Statut :</strong>{" "}
            {row.est_utilisee ? "Utilisée" : "Non utilisée"}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              mt: 2,
              p: 2,
              backgroundColor: "#fef2f2",
              borderRadius: "8px",
              color: "#dc2626",
            }}
          >
            ⚠️ Cette action est irréversible et supprimera définitivement
            l'invitation.
          </Typography>
        </DialogContent>

        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={handleCancelDelete}
            variant="outlined"
            sx={{ borderRadius: "8px" }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            sx={{
              backgroundColor: DESTRUCTIVE,
              "&:hover": { backgroundColor: "#dc2626" },
              borderRadius: "8px",
            }}
          >
            Supprimer définitivement
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
