import React from "react";
import { GridColDef, GridRenderCellParams } from "@mui/x-data-grid";
import { Chip } from "@mui/material";
import { InvitationDash } from "@/domain/models";
import { GREEN, EVENT_COLOR } from "@/shared/constants/Color";
import { DashInvitationActions } from "./DashInvitationActions";

/**
 * Fonction qui retourne les colonnes pour le DataGrid des invitations dash
 */
export const DashInvitationColumns = (): GridColDef[] => {
  return [
    {
      field: "email",
      headerName: "Email",
      width: 250,
      headerClassName: "font-semibold",
    },
    {
      field: "token",
      headerName: "Token",
      width: 200,
      headerClassName: "font-semibold",
      renderCell: (params: GridRenderCellParams<InvitationDash>) => (
        <span
          style={{
            fontFamily: "monospace",
            fontSize: "0.75rem",
            color: "#666",
          }}
        >
          {params.value?.substring(0, 20)}...
        </span>
      ),
    },
    {
      field: "est_utilisee",
      headerName: "Statut",
      width: 120,
      renderCell: (params: GridRenderCellParams<InvitationDash>) => {
        const estUtilisee = params.value as boolean;
        return (
          <Chip
            label={estUtilisee ? "Utilisée" : "Non utilisée"}
            sx={{
              backgroundColor: estUtilisee ? GREEN : EVENT_COLOR,
              color: "white",
              fontWeight: 500,
              fontSize: "0.75rem",
            }}
            size="small"
          />
        );
      },
      headerClassName: "font-semibold",
    },
    {
      field: "cree_le",
      headerName: "Date de création",
      width: 180,
      headerClassName: "font-semibold",
      renderCell: (params: GridRenderCellParams<InvitationDash>) => {
        if (!params.value) return "Non définie";
        return new Date(params.value).toLocaleDateString("fr-FR", {
          year: "numeric",
          month: "short",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        });
      },
    },
    {
      field: "cree_par",
      headerName: "Créée par (ID)",
      width: 120,
      headerClassName: "font-semibold",
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      renderCell: (params: GridRenderCellParams<InvitationDash>) => (
        <DashInvitationActions row={params.row} />
      ),
      headerClassName: "font-semibold",
      sortable: false,
      filterable: false,
    },
  ];
};
