import React from "react";
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  <PERSON>ton,
  Typography,
  Box,
  Chip,
  Divider,
  IconButton,
} from "@mui/material";
import { X, Mail, Calendar, User, Key, CheckCircle, XCircle } from "lucide-react";
import { InvitationDash } from "@/domain/models";
import { GREEN, DESTRUCTIVE, EVENT_COLOR } from "@/shared/constants/Color";

interface DashInvitationDetailModalProps {
  open: boolean;
  onClose: () => void;
  data: InvitationDash | null;
  onMarkAsUsed?: (id: number) => void;
  onDelete?: (id: number) => void;
}

/**
 * Modal de détails pour les invitations dash
 * Affiche les informations détaillées et permet les actions
 */
export const DashInvitationDetailModal: React.FC<DashInvitationDetailModalProps> = ({
  open,
  onClose,
  data,
  onMarkAsUsed,
  onDelete,
}) => {
  if (!data) return null;

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Non définie";
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusChip = (estUtilisee: boolean) => {
    if (estUtilisee) {
      return (
        <Chip
          icon={<CheckCircle size={16} />}
          label="Utilisée"
          sx={{
            backgroundColor: GREEN,
            color: "white",
            fontWeight: 500,
          }}
        />
      );
    }
    return (
      <Chip
        icon={<XCircle size={16} />}
        label="Non utilisée"
        sx={{
          backgroundColor: EVENT_COLOR,
          color: "white",
          fontWeight: 500,
        }}
      />
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 2,
        }}
      >
        <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
          Détails de l'invitation
        </Typography>
        <IconButton onClick={onClose} size="small">
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 0 }}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
          {/* Statut */}
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Statut
            </Typography>
            {getStatusChip(data.est_utilisee)}
          </Box>

          <Divider />

          {/* Informations principales */}
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Mail size={20} color="#666" />
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Email
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {data.email}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Key size={20} color="#666" />
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Token
                </Typography>
                <Typography 
                  variant="body2" 
                  color="text.secondary"
                  sx={{ 
                    fontFamily: "monospace",
                    fontSize: "0.75rem",
                    wordBreak: "break-all"
                  }}
                >
                  {data.token}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Calendar size={20} color="#666" />
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Date de création
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatDate(data.cree_le)}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <User size={20} color="#666" />
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Créée par (ID)
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {data.cree_par}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 2, gap: 1 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{ borderRadius: "8px" }}
        >
          Fermer
        </Button>
        
        {!data.est_utilisee && onMarkAsUsed && (
          <Button
            onClick={() => onMarkAsUsed(data.id)}
            variant="contained"
            sx={{
              backgroundColor: GREEN,
              "&:hover": { backgroundColor: "#059669" },
              borderRadius: "8px",
            }}
          >
            Marquer comme utilisée
          </Button>
        )}
        
        {onDelete && (
          <Button
            onClick={() => onDelete(data.id)}
            variant="contained"
            sx={{
              backgroundColor: DESTRUCTIVE,
              "&:hover": { backgroundColor: "#dc2626" },
              borderRadius: "8px",
            }}
          >
            Supprimer
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default DashInvitationDetailModal;
