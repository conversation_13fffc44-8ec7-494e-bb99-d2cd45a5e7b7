import React, { createContext, ReactNode } from "react";
import { InvitationDash } from "@/domain/models";

interface DashInvitationContextType {
  handleViewDetails: (invitation: InvitationDash) => void;
  handleMarkAsUsed: (id: number) => void;
  handleDelete: (id: number) => void;
}

export const DashInvitationContext = createContext<DashInvitationContextType | null>(null);

interface DashInvitationProviderProps {
  children: ReactNode;
  value: DashInvitationContextType;
}

/**
 * Provider pour le contexte des actions des invitations dash
 * Permet de partager les fonctions d'action entre les composants
 */
export const DashInvitationProvider: React.FC<DashInvitationProviderProps> = ({
  children,
  value,
}) => {
  return (
    <DashInvitationContext.Provider value={value}>
      {children}
    </DashInvitationContext.Provider>
  );
};
