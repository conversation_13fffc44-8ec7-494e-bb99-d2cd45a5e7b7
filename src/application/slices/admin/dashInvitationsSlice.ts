import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import GetInvitationsDashRepository from "@/infrastructure/repositories/invitationDash/GetInvitationsDashRepository";
import { GetInvitationsDashUsecase } from "@/domain/usecases/invitationDash/GetInvitationsDashUsecase";
import MarkInvitationDashAsUsedRepository from "@/infrastructure/repositories/invitationDash/MarkInvitationDashAsUsedRepository";
import { MarkInvitationDashAsUsedUsecase } from "@/domain/usecases/invitationDash/MarkInvitationDashAsUsedUsecase";
import DeleteInvitationDashRepository from "@/infrastructure/repositories/invitationDash/DeleteInvitationDashRepository";
import { DeleteInvitationDashUsecase } from "@/domain/usecases/invitationDash/DeleteInvitationDashUsecase";
import GenerateDashInvitationUsecase from "@/domain/usecases/dash/GenerateDashInvitationUsecase";
import { CreateInvitationDashUsecase } from "@/domain/usecases/invitationDash/CreateInvitationDashUsecase";
import CreateInvitationDashRepository from "@/infrastructure/repositories/invitationDash/CreateInvitationDashRepository";
import { VerifyAndCheckEmail } from "@/domain/services/VerifyAndCheckEmail";
import { GetAuthenticatedUserUsecase } from "@/domain/usecases/user/userUsecase";
import GetAuthenticatedUserRepository from "@/infrastructure/repositories/user/GetAuthenticatedUserRepository";
import { GetUserByEmailUsecase } from "@/domain/usecases/user/userUsecase";
import GetUserByEmailRepository from "@/infrastructure/repositories/user/GetUserByEmailRepository";
import DASHInvitationEmailService from "@/services/customEmailService/DASHInvitationEmailService";

interface GenerateDashInvitationParams {
  email: string;
  organizationName: string;
}

const initialState = {
  invitations: [],
  loading: false,
  error: null,
};

export const getDashInvitations = createAsyncThunk(
  "dashInvitations/getDashInvitations",
  async (_, { rejectWithValue }) => {
    try {
      const getInvitationsDashRepository = new GetInvitationsDashRepository();
      const getInvitationsDashUsecase = new GetInvitationsDashUsecase(
        getInvitationsDashRepository
      );
      const invitations = await getInvitationsDashUsecase.execute();
      return invitations;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const markDashInvitationAsUsed = createAsyncThunk(
  "dashInvitations/markDashInvitationAsRead",
  async (invitation_id: number, { rejectWithValue }) => {
    try {
      const markInvitationDashAsUsedRepository =
        new MarkInvitationDashAsUsedRepository();
      const markInvitationDashAsUsedUsecase =
        new MarkInvitationDashAsUsedUsecase(markInvitationDashAsUsedRepository);
      const updatedInvitation =
        await markInvitationDashAsUsedUsecase.execute(invitation_id);
      return updatedInvitation;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const deleteDashInvitation = createAsyncThunk(
  "dashInvitations/deleteDashInvitation",
  async (invitation_id: number, { rejectWithValue }) => {
    try {
      const deleteInvitationDashRepository =
        new DeleteInvitationDashRepository();
      const deleteInvitationDashUsecase = new DeleteInvitationDashUsecase(
        deleteInvitationDashRepository
      );
      await deleteInvitationDashUsecase.execute(invitation_id);
      return invitation_id;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const generateDashInvitation = createAsyncThunk(
  "dashInvitations/generateDashInvitation",
  async (params: GenerateDashInvitationParams, { rejectWithValue }) => {
    try {
      // Instanciation des repositories
      const getUserByEmailRepository = new GetUserByEmailRepository();
      const getAuthenticatedUserRepository =
        new GetAuthenticatedUserRepository();
      const createInvitationDashRepository =
        new CreateInvitationDashRepository();

      // Instanciation des usecases
      const getUserByEmailUsecase = new GetUserByEmailUsecase(
        getUserByEmailRepository
      );
      const getAuthenticatedUserUsecase = new GetAuthenticatedUserUsecase(
        getAuthenticatedUserRepository
      );
      const createInvitationDashUsecase = new CreateInvitationDashUsecase(
        createInvitationDashRepository
      );

      // Instanciation des services
      const verifyAndCheckEmail = new VerifyAndCheckEmail(
        getUserByEmailUsecase
      );
      const dashInvitationEmailService = new DASHInvitationEmailService();

      // Instanciation du usecase principal
      const generateDashInvitationUsecase = new GenerateDashInvitationUsecase(
        verifyAndCheckEmail,
        getAuthenticatedUserUsecase,
        getUserByEmailUsecase,
        createInvitationDashUsecase,
        dashInvitationEmailService
      );

      // Exécution
      const result = await generateDashInvitationUsecase.execute(
        params.email,
        params.organizationName
      );

      return result;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const dashInvitationsSlice = createSlice({
  name: "dashInvitations",
  initialState,
  reducers: {
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getDashInvitations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDashInvitations.fulfilled, (state, action) => {
        state.loading = false;
        state.invitations = action.payload;
      })
      .addCase(getDashInvitations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(markDashInvitationAsUsed.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(markDashInvitationAsUsed.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.invitations.findIndex(
          (invitation) => invitation.id === action.payload.id
        );
        if (index !== -1) {
          state.invitations[index] = action.payload;
        }
      })
      .addCase(markDashInvitationAsUsed.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(deleteDashInvitation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteDashInvitation.fulfilled, (state, action) => {
        state.loading = false;
        state.invitations = state.invitations.filter(
          (invitation) => invitation.id !== action.payload
        );
      })
      .addCase(deleteDashInvitation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(generateDashInvitation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateDashInvitation.fulfilled, (state, action) => {
        state.loading = false;
        // Ajouter la nouvelle invitation à la liste
        state.invitations.unshift(action.payload);
      })
      .addCase(generateDashInvitation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setIsLoading, setError } = dashInvitationsSlice.actions;
export default dashInvitationsSlice.reducer;
