import { IDeleteInvitationDashRepository } from "@/domain/interfaces/repositories/invitationDash";
import { IDeleteInvitationDashUsecase } from "@/domain/interfaces/usecases/invitationDash";

export class DeleteInvitationDashUsecase implements IDeleteInvitationDashUsecase {
  constructor(
    private readonly deleteInvitationDashRepository: IDeleteInvitationDashRepository
  ) {}

  async execute(invitation_id: number): Promise<void> {
    try {
      // Ici, vous pourrez ajouter des validations supplémentaires
      // Par exemple, vérifier si l'invitation existe avant de la supprimer
      await this.deleteInvitationDashRepository.execute(invitation_id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }
}
